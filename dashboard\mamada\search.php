<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Conexión
$host = '127.0.0.1';
$dbname = 'u636704306_Dictionaries';
$user = 'u636704306_Kevs';
$pass = '5@8W>|NRe';
$charset = 'utf8mb4';
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
];

// Funciones
function limpiarGloss($gloss) {
    return preg_replace('/\s*{\s*"pS":.*$/s', '', $gloss);
}
function esJapones($text) {
    return preg_match('/[\x{3040}-\x{30FF}\x{4E00}-\x{9FFF}]/u', $text);
}

// Obtener y limpiar parámetro
$q = isset($_GET['q']) ? strtolower(trim($_GET['q'])) : '';
if ($q === '') {
    echo json_encode([]);
    exit;
}
$wildcard = "%$q%";

try {
    $pdo = new PDO($dsn, $user, $pass, $options);

    if (esJapones($q)) {
        // Japonés
        $sqlIds = "
            SELECT DISTINCT ID,
                CASE
                    WHEN LOWER(KJ_tx) = ? OR LOWER(KN_tx) = ? THEN 1
                    ELSE 2
                END AS priority
            FROM registros_full
            WHERE 
                LOWER(KJ_tx) LIKE ? OR 
                LOWER(KN_tx) LIKE ?
            ORDER BY priority ASC
        ";
        $stmtIds = $pdo->prepare($sqlIds);
        $stmtIds->execute([$q, $q, $wildcard, $wildcard]);
    } else {
        // Español/Inglés
        $sqlIds = "
            SELECT DISTINCT ID,
                CASE
                    WHEN LOWER(SE_GL_1_tx) = ? OR LOWER(SE_GL_2_tx) = ? THEN 1
                    ELSE 2
                END AS priority
            FROM registros_full
            WHERE 
                LOWER(SE_GL_1_tx) LIKE ? OR 
                LOWER(SE_GL_2_tx) LIKE ?
            ORDER BY priority ASC
        ";
        $stmtIds = $pdo->prepare($sqlIds);
        $stmtIds->execute([$q, $q, $wildcard, $wildcard]);
    }

    $rows = $stmtIds->fetchAll();
    if (count($rows) === 0) {
        echo json_encode([]);
        exit;
    }

    $ids = [];
    $priorities = [];
    foreach ($rows as $row) {
        $ids[] = $row['ID'];
        $priorities[$row['ID']] = (int)$row['priority'];
    }

    $placeholders = implode(',', array_fill(0, count($ids), '?'));
    $sql = "SELECT * FROM registros_full WHERE ID IN ($placeholders)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($ids);
    $results = $stmt->fetchAll();

    foreach ($results as &$entry) {
        $entry['KJ_co'] = $entry['KJ_co'] === '1';
        $entry['KN_co'] = $entry['KN_co'] === '1';
        $entry['KJ_tg'] = isset($entry['KJ_tg']) ? explode(',', $entry['KJ_tg']) : [];
        $entry['KN_tg'] = isset($entry['KN_tg']) ? explode(',', $entry['KN_tg']) : [];
        $entry['SE_pS'] = isset($entry['SE_pS']) ? explode(',', $entry['SE_pS']) : [];
        $entry['SE_ms'] = isset($entry['SE_ms']) ? explode(',', $entry['SE_ms']) : [];
        $entry['SE_GL_1_tx'] = limpiarGloss($entry['SE_GL_1_tx'] ?? '');
        $entry['SE_GL_2_tx'] = limpiarGloss($entry['SE_GL_2_tx'] ?? '');
        $entry['jlptLevel'] = $entry['jlptLevel'] ?? null;
        $entry['id'] = $entry['ID'] ?? $entry['id'] ?? null;
    }

    // Reordenar resultados por prioridad exacta → parcial
    usort($results, function($a, $b) use ($priorities) {
        $pa = $priorities[$a['ID']] ?? 2;
        $pb = $priorities[$b['ID']] ?? 2;
        return $pa <=> $pb;
    });

    echo json_encode($results);
} catch (PDOException $e) {
    $msg = '❌ Error en consulta SQL: ' . $e->getMessage();
    file_put_contents('log.txt', $msg);
    error_log($msg);
    http_response_code(500);
    echo json_encode(['error' => $msg]);
    exit;
}





