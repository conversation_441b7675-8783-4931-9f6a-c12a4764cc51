<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search API</title>
</head>
<body>
    <h1>Test Search API</h1>
    <input type="text" id="searchInput" placeholder="Buscar palabra..." value="agua">
    <button onclick="testSearch()">Buscar</button>
    <div id="results"></div>

    <script>
        async function testSearch() {
            const query = document.getElementById('searchInput').value;
            const resultsDiv = document.getElementById('results');
            
            try {
                const response = await fetch(`search_mock.php?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                resultsDiv.innerHTML = '<h2>Resultados:</h2><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultsDiv.innerHTML = '<h2>Error:</h2><pre>' + error.message + '</pre>';
            }
        }
        
        // Test automático al cargar
        window.onload = () => testSearch();
    </script>
</body>
</html>
