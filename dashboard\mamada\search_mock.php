<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Datos de prueba simulados
$mockData = [
    [
        'ID' => 1,
        'KJ_tx' => '水',
        'KJ_co' => '1',
        'KJ_tg' => '',
        'KN_tx' => 'みず',
        'KN_co' => '1', 
        'KN_tg' => '',
        'SE_GL_1_tx' => 'agua',
        'SE_GL_2_tx' => 'water',
        'SE_pS' => 'n',
        'SE_ms' => '',
        'jlptLevel' => 'N5'
    ],
    [
        'ID' => 2,
        'KJ_tx' => '本',
        'KJ_co' => '1',
        'KJ_tg' => '',
        'KN_tx' => 'ほん',
        'KN_co' => '1',
        'KN_tg' => '',
        'SE_GL_1_tx' => 'libro',
        'SE_GL_2_tx' => 'book',
        'SE_pS' => 'n',
        'SE_ms' => '',
        'jlptLevel' => 'N5'
    ],
    [
        'ID' => 3,
        'KJ_tx' => '食べる',
        'KJ_co' => '1',
        'KJ_tg' => '',
        'KN_tx' => 'たべる',
        'KN_co' => '1',
        'KN_tg' => '',
        'SE_GL_1_tx' => 'comer',
        'SE_GL_2_tx' => 'to eat',
        'SE_pS' => 'v1',
        'SE_ms' => '',
        'jlptLevel' => 'N5'
    ],
    [
        'ID' => 4,
        'KJ_tx' => '学校',
        'KJ_co' => '1',
        'KJ_tg' => '',
        'KN_tx' => 'がっこう',
        'KN_co' => '1',
        'KN_tg' => '',
        'SE_GL_1_tx' => 'escuela',
        'SE_GL_2_tx' => 'school',
        'SE_pS' => 'n',
        'SE_ms' => '',
        'jlptLevel' => 'N5'
    ]
];

// Obtener parámetro de búsqueda
$q = isset($_GET['q']) ? strtolower(trim($_GET['q'])) : '';
if ($q === '') {
    echo json_encode([]);
    exit;
}

// Filtrar datos simulados
$results = [];
foreach ($mockData as $entry) {
    $matches = false;
    
    // Buscar en japonés
    if (strpos(strtolower($entry['KJ_tx']), $q) !== false ||
        strpos(strtolower($entry['KN_tx']), $q) !== false) {
        $matches = true;
    }
    
    // Buscar en español/inglés
    if (strpos(strtolower($entry['SE_GL_1_tx']), $q) !== false ||
        strpos(strtolower($entry['SE_GL_2_tx']), $q) !== false) {
        $matches = true;
    }
    
    if ($matches) {
        // Procesar datos como en el archivo original
        $entry['KJ_co'] = $entry['KJ_co'] === '1';
        $entry['KN_co'] = $entry['KN_co'] === '1';
        $entry['KJ_tg'] = isset($entry['KJ_tg']) ? explode(',', $entry['KJ_tg']) : [];
        $entry['KN_tg'] = isset($entry['KN_tg']) ? explode(',', $entry['KN_tg']) : [];
        $entry['SE_pS'] = isset($entry['SE_pS']) ? explode(',', $entry['SE_pS']) : [];
        $entry['SE_ms'] = isset($entry['SE_ms']) ? explode(',', $entry['SE_ms']) : [];
        $entry['id'] = $entry['ID'];
        
        $results[] = $entry;
    }
}

echo json_encode($results);
?>
