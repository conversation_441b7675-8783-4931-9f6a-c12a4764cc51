// Wanakana mock para testing
window.wanakana = {
    isJapanese: function(text) {
        return /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text);
    },
    isRomaji: function(text) {
        return /^[a-zA-Z\s]+$/.test(text);
    },
    isKana: function(text) {
        return /[\u3040-\u309F\u30A0-\u30FF]/.test(text);
    },
    isKanji: function(text) {
        return /[\u4E00-\u9FAF]/.test(text);
    },
    toHiragana: function(text, options) {
        // Conversión básica de romaji a hiragana
        const conversions = {
            'a': 'あ', 'i': 'い', 'u': 'う', 'e': 'え', 'o': 'お',
            'ka': 'か', 'ki': 'き', 'ku': 'く', 'ke': 'け', 'ko': 'こ',
            'ga': 'が', 'gi': 'ぎ', 'gu': 'ぐ', 'ge': 'げ', 'go': 'ご',
            'sa': 'さ', 'shi': 'し', 'su': 'す', 'se': 'せ', 'so': 'そ',
            'za': 'ざ', 'ji': 'じ', 'zu': 'ず', 'ze': 'ぜ', 'zo': 'ぞ',
            'ta': 'た', 'chi': 'ち', 'tsu': 'つ', 'te': 'て', 'to': 'と',
            'da': 'だ', 'di': 'ぢ', 'du': 'づ', 'de': 'で', 'do': 'ど',
            'na': 'な', 'ni': 'に', 'nu': 'ぬ', 'ne': 'ね', 'no': 'の',
            'ha': 'は', 'hi': 'ひ', 'fu': 'ふ', 'he': 'へ', 'ho': 'ほ',
            'ba': 'ば', 'bi': 'び', 'bu': 'ぶ', 'be': 'べ', 'bo': 'ぼ',
            'pa': 'ぱ', 'pi': 'ぴ', 'pu': 'ぷ', 'pe': 'ぺ', 'po': 'ぽ',
            'ma': 'ま', 'mi': 'み', 'mu': 'む', 'me': 'め', 'mo': 'も',
            'ya': 'や', 'yu': 'ゆ', 'yo': 'よ',
            'ra': 'ら', 'ri': 'り', 'ru': 'る', 're': 'れ', 'ro': 'ろ',
            'wa': 'わ', 'wo': 'を', 'n': 'ん',
            'mizu': 'みず', 'hon': 'ほん', 'taberu': 'たべる', 'gakkou': 'がっこう',
            'agua': 'agua', 'libro': 'libro', 'comer': 'comer', 'escuela': 'escuela'
        };
        
        let result = text.toLowerCase();
        for (let romaji in conversions) {
            result = result.replace(new RegExp(romaji, 'g'), conversions[romaji]);
        }
        return result;
    },
    toKatakana: function(text) {
        // Conversión básica de hiragana a katakana
        const hiragana = 'あいうえおかきくけこがぎぐげごさしすせそざじずぜぞたちつてとだぢづでどなにぬねのはひふへほばびぶべぼぱぴぷぺぽまみむめもやゆよらりるれろわをん';
        const katakana = 'アイウエオカキクケコガギグゲゴサシスセソザジズゼゾタチツテトダヂヅデドナニヌネノハヒフヘホバビブベボパピプペポマミムメモヤユヨラリルレロワヲン';
        
        return text.split('').map(char => {
            const index = hiragana.indexOf(char);
            return index !== -1 ? katakana[index] : char;
        }).join('');
    }
};
